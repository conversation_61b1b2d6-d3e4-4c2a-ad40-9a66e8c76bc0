#!/usr/bin/env python3
"""
Test script to verify all components work correctly.
"""

import sys
import os

def test_imports():
    """Test all required imports."""
    print("Testing imports...")
    
    try:
        print("  ✓ Testing attendance_tracker...")
        from attendance_tracker import AttendanceTracker
        
        print("  ✓ Testing yolo_processor...")
        from yolo_processor import YOLOProcessor
        
        print("  ✓ Testing video_streaming...")
        from video_streaming import WebcamStreamer
        
        print("  ✓ Testing gradio...")
        import gradio as gr
        
        print("  ✓ Testing requests...")
        import requests
        
        print("  ✓ Testing threading...")
        import threading
        
        print("✅ All imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_model_file():
    """Test if YOLO model file exists."""
    print("\nTesting model file...")
    
    model_path = '../models/yolov8n.pt'
    if os.path.exists(model_path):
        print(f"  ✓ Model file found: {model_path}")
        return True
    else:
        print(f"  ❌ Model file not found: {model_path}")
        return False

def test_attendance_tracker():
    """Test attendance tracker functionality."""
    print("\nTesting AttendanceTracker...")
    
    try:
        from attendance_tracker import AttendanceTracker
        
        # Test initialization
        tracker = AttendanceTracker(min_frames=2, csv_path="test_attendance.csv")
        print("  ✓ AttendanceTracker initialized")
        
        # Test tracking
        mock_detections = [
            {
                'class_name': 'person',
                'confidence': 0.85,
                'box': [100, 100, 200, 200]
            }
        ]
        
        # Track multiple times
        for i in range(3):
            tracker.track_objects(mock_detections, "test_session")
        
        # Check if attendance was logged
        log = tracker.get_attendance_log()
        if len(log) > 0:
            print(f"  ✓ Attendance logged: {len(log)} records")
        else:
            print("  ⚠ No attendance logged (may need more frames)")
        
        print("✅ AttendanceTracker test passed!")
        return True
        
    except Exception as e:
        print(f"❌ AttendanceTracker test failed: {e}")
        return False

def test_gradio_components():
    """Test Gradio components that caused issues."""
    print("\nTesting Gradio components...")
    
    try:
        import gradio as gr
        
        # Test Dataframe without height parameter
        df = gr.Dataframe(
            headers=["Name", "Timestamp"],
            datatype=["str", "str"],
            interactive=False
        )
        print("  ✓ Dataframe component works")
        
        # Test other components
        btn = gr.Button("Test", variant="primary")
        print("  ✓ Button component works")
        
        file_comp = gr.File(label="Test", visible=False)
        print("  ✓ File component works")
        
        print("✅ Gradio components test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Gradio components test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Running comprehensive tests for the fixed app...\n")
    
    tests = [
        test_imports,
        test_model_file,
        test_attendance_tracker,
        test_gradio_components
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print(f"\n{'='*60}")
    print("📊 TEST SUMMARY:")
    print(f"{'='*60}")
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test.__name__}: {status}")
    
    all_passed = all(results)
    overall_status = "✅ ALL TESTS PASSED" if all_passed else "❌ SOME TESTS FAILED"
    print(f"\nOverall: {overall_status}")
    
    if all_passed:
        print(f"\n🎉 Your app should now work correctly!")
        print(f"Run: python app.py")
        print(f"Or for FastAPI only: python run_fastapi_only.py")
    else:
        print(f"\n🔧 Please fix the failing tests before running the app.")
    
    print(f"{'='*60}")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
