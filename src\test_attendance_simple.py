#!/usr/bin/env python3
"""
Simple test to add sample attendance records for testing the interface.
"""

import csv
from datetime import datetime

def create_sample_attendance():
    """Create sample attendance records for testing."""
    sample_records = [
        {'name': 'person', 'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')},
        {'name': 'car', 'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')},
        {'name': 'person', 'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')},
    ]
    
    # Write to CSV file
    with open('attendance_log.csv', 'w', newline='') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=['name', 'timestamp'])
        writer.writeheader()
        writer.writerows(sample_records)
    
    print(f"✅ Created sample attendance log with {len(sample_records)} records")
    print("📁 File: attendance_log.csv")
    
    # Display the records
    print("\n📋 Sample records:")
    for i, record in enumerate(sample_records, 1):
        print(f"  {i}. {record['name']} - {record['timestamp']}")

if __name__ == "__main__":
    create_sample_attendance()
