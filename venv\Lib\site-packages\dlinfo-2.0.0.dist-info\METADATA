Metadata-Version: 2.1
Name: dlinfo
Version: 2.0.0
Summary: Python wrapper for libc's dlinfo and dyld_find on Mac
Home-page: https://github.com/fphammerle/python-dlinfo
Maintainer: <PERSON>
Maintainer-email: <EMAIL>
License: MIT
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: POSIX :: Linux
Requires-Python: >=3.9
License-File: LICENSE

python-dlinfo
=============

Python wrapper for libc's dlinfo

Install
-------

.. code:: sh

   pip install dlinfo
   # or
   pipenv install dlinfo

Usage
-----

.. code:: python

   >>> from dlinfo import DLInfo
   >>> lib = ctypes.cdll.LoadLibrary(ctypes.util.find_library('c'))
   >>> dlinfo = DLInfo(lib)
   >>> dlinfo.path
   '/lib/x86_64-linux-gnu/libc.so.6'
