                    print(f"[Processor] Processed {process_frame_count} frames in 1s. FPS: {process_frame_count / (time.time() - process_start_time):.2f}")
                    process_frame_count = 0
                    process_start_time = time.time()
            except Exception as e:
                print(f"[Processor] Error processing batch: {e}")
                import traceback
                traceback.print_exc()

        await asyncio.sleep(0.001) # Small sleep to yield control and prevent tight loop

@app.on_event("startup")
async def startup_event():
    # Start the frame processing task in the background
    asyncio.create_task(frame_processor_task())

@app.websocket("/ws/webcam_stream")
async def websocket_webcam_stream(websocket: WebSocket):
    session_id = None
    try:
        async with client_queues_lock:
            if len(client_queues) >= MAX_CLIENTS:
                await websocket.close(code=1013, reason=f"Too many clients connected. Max: {MAX_CLIENTS}")
                print(f"[WebSocket] Rejected connection: Too many clients (max {MAX_CLIENTS})")
                return

        await websocket.accept()
        session_id = str(uuid.uuid4())
        print(f"[WebSocket] Client connected: /ws/webcam_stream, session_id: {session_id}")
        
        async with client_queues_lock:
            # Using deque to allow discarding old elements easily
            client_queues[session_id] = {
                "input": asyncio.Queue(maxsize=MAX_QUEUE_SIZE_INPUT), 
                "output": asyncio.Queue(maxsize=MAX_QUEUE_SIZE_OUTPUT)
            }

        # Create separate tasks for receiving and sending to avoid blocking each other
        receive_task = asyncio.create_task(receive_frames(websocket, session_id))
        send_task = asyncio.create_task(send_processed_frames(websocket, session_id))

        await asyncio.gather(receive_task, send_task)

    except WebSocketDisconnect as e:
        print(f"[WebSocket] Client {session_id} disconnected: {e.code} - {e.reason}")
    except RuntimeError as e:
        print(f"[WebSocket] Runtime error for client {session_id}: {e}")
    except Exception as e:
        print(f"[WebSocket] Unhandled error in websocket_webcam_stream for session {session_id}: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Clean up resources associated with the disconnected client
        async with client_queues_lock:
            if session_id and session_id in client_queues:
                del client_queues[session_id]
                print(f"[WebSocket] Cleaned up queues for session {session_id}")
        
        if websocket.client_state == WebSocketState.CONNECTED:
            try:
                await websocket.close(code=1000, reason="Normal closure")
                print(f"[WebSocket] Closed WebSocket for session {session_id}")
            except Exception as e:
                print(f"[WebSocket] Error closing WebSocket for session {session_id}: {e}")
