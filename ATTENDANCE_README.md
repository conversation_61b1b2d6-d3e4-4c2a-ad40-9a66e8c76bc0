# Attendance Tracking Feature

This document describes the new attendance tracking functionality added to the YOLOv8 face detection application.

## Overview

The attendance tracking feature automatically logs detected objects (like people) when they appear consistently across multiple frames. This is useful for monitoring attendance in classrooms, meetings, or other scenarios.

## How It Works

1. **Object Detection**: The YOLOv8 model detects objects in each frame
2. **Object Tracking**: Objects are tracked across frames using position-based IDs
3. **Persistence Check**: Only objects detected for a minimum number of consecutive frames are logged
4. **Attendance Logging**: Qualified objects are recorded with name and timestamp

## Configuration

### Minimum Frames Threshold
- Default: 30 frames
- Configurable in `AttendanceTracker` initialization
- Objects must appear for this many frames to be logged

### CSV Output
- Default file: `attendance_log.csv`
- Contains columns: `name`, `timestamp`
- Automatically created and updated

## Files Modified/Added

### New Files
- `src/attendance_tracker.py` - Core attendance tracking logic
- `src/test_attendance.py` - Test script for attendance functionality
- `ATTENDANCE_README.md` - This documentation

### Modified Files
- `src/yolo_processor.py` - Added detection data extraction
- `src/video_streaming.py` - Integrated attendance tracking and API endpoints
- `src/app.py` - Added attendance table to Gradio interface

## API Endpoints

### GET /api/attendance
Returns current attendance log as JSON:
```json
{
  "attendance": [
    {"name": "person", "timestamp": "2024-01-15 10:30:45"},
    {"name": "person", "timestamp": "2024-01-15 10:31:20"}
  ]
}
```

### GET /api/attendance/csv
Downloads the attendance log as a CSV file.

## User Interface

### Web Interface (FastAPI)
- Real-time video stream with object detection
- Live attendance table that updates every 5 seconds
- Refresh button for manual updates
- Clean, responsive design

### Gradio Interface
- Embedded video stream
- Side-by-side attendance table
- Auto-refresh every 5 seconds
- Download CSV functionality
- Refresh button for manual updates

## Usage

### Running the Application
```bash
# Start the application
python src/app.py
```

### Testing the Functionality
```bash
# Run tests
python src/test_attendance.py
```

### Accessing the Interface
- FastAPI Web Interface: http://localhost:8000
- Gradio Interface: http://localhost:7860

## Technical Details

### Object Tracking Algorithm
- Simple position-based tracking using bounding box coordinates
- Object ID format: `{session_id}_{class_name}_{x}_{y}`
- Tracks objects per client session to avoid conflicts

### Duplicate Prevention
- Objects are only logged once per tracking session
- Uses a set to track already-logged object IDs
- Prevents spam logging of the same object

### Performance Considerations
- Minimal impact on detection performance
- Efficient tracking using dictionaries and sets
- Automatic cleanup of old tracking data

## Customization

### Adjusting Detection Sensitivity
Modify the minimum frames threshold in `video_streaming.py`:
```python
self.attendance_tracker = AttendanceTracker(min_frames=20)  # Lower = more sensitive
```

### Changing Object Classes
The system tracks all detected object classes. To filter specific classes, modify the detection processing in `video_streaming.py`.

### Custom CSV Format
Modify the CSV headers and data structure in `attendance_tracker.py` to include additional fields like confidence scores or bounding box coordinates.

## Troubleshooting

### Common Issues

1. **No detections logged**: Check if objects appear for enough consecutive frames
2. **Duplicate entries**: Ensure the attendance tracker is properly initialized
3. **CSV not updating**: Check file permissions and disk space
4. **API endpoints not working**: Verify FastAPI server is running

### Debug Information
- Attendance logging events are printed to console
- Check browser developer tools for API request errors
- Monitor the log container in the web interface

## Future Enhancements

Potential improvements for the attendance system:
- Face recognition for person identification
- Configurable detection classes
- Time-based attendance sessions
- Export to different formats (Excel, PDF)
- Integration with external databases
- Email notifications for attendance events
