#!/usr/bin/env python3
"""
Test script to verify the attendance functionality works correctly.
"""

import sys
import os
import numpy as np
from yolo_processor import YOLOProcessor
from attendance_tracker import AttendanceTracker

def test_yolo_processor():
    """Test the YOLOProcessor with detection extraction."""
    print("Testing YOLOProcessor...")
    
    try:
        # Initialize processor
        processor = YOLOProcessor(model_path='models/yolov8n.pt')
        
        # Create a dummy frame
        dummy_frame = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # Test without detections
        print("Testing without detections...")
        processed_frame = processor.process_frames(dummy_frame, return_detections=False)
        print(f"Processed frame shape: {processed_frame.shape}")
        
        # Test with detections
        print("Testing with detections...")
        processed_frame, detections = processor.process_frames(dummy_frame, return_detections=True)
        print(f"Processed frame shape: {processed_frame.shape}")
        print(f"Detections: {detections}")
        
        print("YOLOProcessor test passed!")
        return True
        
    except Exception as e:
        print(f"YOLOProcessor test failed: {e}")
        return False

def test_attendance_tracker():
    """Test the AttendanceTracker."""
    print("\nTesting AttendanceTracker...")
    
    try:
        # Initialize tracker
        tracker = AttendanceTracker(min_frames=2, csv_path="test_attendance.csv")
        
        # Create mock detections
        mock_detections = [
            {
                'class_name': 'person',
                'confidence': 0.85,
                'box': [100, 100, 200, 200]
            },
            {
                'class_name': 'car',
                'confidence': 0.75,
                'box': [300, 300, 400, 400]
            }
        ]
        
        # Track objects multiple times to trigger attendance logging
        session_id = "test_session"
        for i in range(5):
            print(f"Frame {i+1}:")
            attendance = tracker.track_objects(mock_detections, session_id)
            print(f"  Current attendance log: {len(attendance)} records")
        
        # Get final attendance log
        final_log = tracker.get_attendance_log()
        print(f"Final attendance log: {final_log}")
        
        # Test CSV export
        csv_path = tracker.export_to_csv()
        print(f"CSV exported to: {csv_path}")
        
        print("AttendanceTracker test passed!")
        return True
        
    except Exception as e:
        print(f"AttendanceTracker test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("Starting attendance functionality tests...\n")
    
    # Check if model exists
    if not os.path.exists('models/yolov8n.pt'):
        print("Error: YOLO model not found at 'models/yolov8n.pt'")
        print("Please ensure the model file exists before running tests.")
        return False
    
    # Run tests
    yolo_test = test_yolo_processor()
    attendance_test = test_attendance_tracker()
    
    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY:")
    print(f"YOLOProcessor: {'PASS' if yolo_test else 'FAIL'}")
    print(f"AttendanceTracker: {'PASS' if attendance_test else 'FAIL'}")
    print(f"Overall: {'PASS' if yolo_test and attendance_test else 'FAIL'}")
    print(f"{'='*50}")
    
    return yolo_test and attendance_test

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
