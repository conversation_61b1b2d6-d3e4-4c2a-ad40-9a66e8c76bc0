
# import cv2
# import uvicorn
# from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, Request
# from fastapi.responses import HTMLResponse
# from fastapi.staticfiles import StaticFiles
# from pathlib import Path
# import asyncio
# import numpy as np
# import base64
# import time
# import uuid
# from starlette.websockets import WebSocketState

# try:
#     from yolo_processor import process_frame
# except ImportError:
#     print("Error: yolo_processor.py not found. Please ensure it's in the same directory.")
#     exit()

# app = FastAPI()

# app.mount("/static", StaticFiles(directory="static"), name="static")

# client_queues = {}
# client_queues_lock = asyncio.Lock()  # Khóa để đồng bộ hóa truy cập client_queues
# MAX_CLIENTS = 2  # Giới hạn số client đồng thời

# HTML_CONTENT = """
# <!DOCTYPE html>
# <html>
# <head>
#     <title>Webcam Stream with YOLOv8 (Client-side)</title>
#     <style>
#         body { font-family: Arial, sans-serif; display: flex; flex-direction: column; align-items: center; background-color: #f0f0f0; margin: 0; padding: 20px; }
#         h1 { color: #333; }
#         .video-container { display: flex; gap: 20px; margin-top: 20px; }
#         video, img { border: 2px solid #ccc; background-color: #eee; }
#         .controls { margin-top: 20px; }
#         button { padding: 10px 20px; font-size: 16px; cursor: pointer; }
#         .log-container { margin-top: 20px; background-color: #fff; padding: 10px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); width: 80%; max-width: 1200px; height: 150px; overflow-y: scroll; border: 1px solid #ddd;}
#         pre { white-space: pre-wrap; word-wrap: break-word; }
#     </style>
#     <link rel="stylesheet" href="/static/styles.css">
# </head>
# <body>
#     <h1>Real-Time Object Detection (Client Webcam)</h1>
#     <div class="video-container">
#         <div>
#             <h2>Your Webcam (Raw)</h2>
#             <video id="webcamVideo" width="640" height="480" autoplay muted></video>
#         </div>
#         <div>
#             <h2>Processed Output (YOLOv8)</h2>
#             <img id="processedImage" width="640" height="480">
#         </div>
#         <canvas id="webcamCanvas" width="640" height="480" style="display: none;"></canvas>
#     </div>
#     <div class="controls">
#         <button id="startButton">Start Stream</button>
#         <button id="stopButton">Stop Stream</button>
#     </div>
#     <div class="log-container">
#         <pre id="log"></pre>
#     </div>

#     <script>
#         const videoElement = document.getElementById('webcamVideo');
#         const canvasElement = document.getElementById('webcamCanvas');
#         const processedImageElement = document.getElementById('processedImage');
#         const startButton = document.getElementById('startButton');
#         const stopButton = document.getElementById('stopButton');
#         const logElement = document.getElementById('log');
#         const canvasContext = canvasElement.getContext('2d');
#         let ws;
#         let webcamStream;
#         let sendFrameIntervalId;
#         const sendFrameRate = 50;

#         function log(message) {
#             const now = new Date().toLocaleTimeString();
#             logElement.textContent += `[${now}] ${message}\\n`;
#             logElement.scrollTop = logElement.scrollHeight;
#         }

#         function startStream() {
#             if (ws && ws.readyState === WebSocket.OPEN) {
#                 log("Stream already active.");
#                 return;
#             }

#             if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
#                 log("WebRTC is not supported in this browser.");
#                 alert("Your browser does not support WebRTC. Please use a modern browser like Chrome or Firefox.");
#                 return;
#             }

#             log("Attempting to access webcam...");
#             navigator.mediaDevices.getUserMedia({ video: true })
#                 .then(function(stream) {
#                     webcamStream = stream;
#                     videoElement.srcObject = stream;
#                     videoElement.onloadedmetadata = () => {
#                         log("Webcam stream started successfully.");
#                         canvasElement.width = videoElement.videoWidth;
#                         canvasElement.height = videoElement.videoHeight;
#                         processedImageElement.width = videoElement.videoWidth;
#                         processedImageElement.height = videoElement.videoHeight;
#                     };

#                     const publicUrl = window.location.hostname;
#                     const wsProtocol = window.location.protocol === "https:" ? "wss:" : "ws:";
#                     const wsUrl = `${wsProtocol}//${publicUrl}/ws/webcam_stream`;
#                     log(`Connecting to WebSocket at ${wsUrl}...`);
#                     ws = new WebSocket(wsUrl);
#                     ws.binaryType = 'arraybuffer';

#                     ws.onopen = function() {
#                         log("WebSocket connected. Starting to send frames...");
#                         sendFrameIntervalId = setInterval(sendFrame, sendFrameRate);
#                         startButton.disabled = true;
#                         stopButton.disabled = false;
#                     };

#                     ws.onmessage = function(event) {
#                         const blob = new Blob([event.data], {type: 'image/jpeg'});
#                         const url = URL.createObjectURL(blob);
#                         if (processedImageElement.src && processedImageElement.src.startsWith('blob:')) {
#                             URL.revokeObjectURL(processedImageElement.src);
#                         }
#                         processedImageElement.src = url;
#                     };

#                     ws.onclose = function(event) {
#                         log("WebSocket disconnected. Code: " + event.code + ", Reason: " + event.reason);
#                         stopSendingFrames();
#                         startButton.disabled = false;
#                         stopButton.disabled = true;
#                     };

#                     ws.onerror = function(error) {
#                         log("WebSocket error: " + (error.message || error));
#                         console.error("WebSocket error:", error);
#                         ws.close();
#                     };

#                 })
#                 .catch(function(error) {
#                     log("Error accessing webcam: " + error.name + " - " + error.message);
#                     console.error("Error accessing webcam: ", error);
#                     alert("Could not access webcam. Please ensure it's connected and you've granted permission.");
#                 });
#         }

#         function sendFrame() {
#             if (ws && ws.readyState === WebSocket.OPEN && videoElement.videoWidth > 0 && videoElement.readyState >= 2) {
#                 canvasContext.drawImage(videoElement, 0, 0, canvasElement.width, canvasElement.height);
#                 canvasElement.toBlob(function(blob) {
#                     if (blob) {
#                         const reader = new FileReader();
#                         reader.onload = function() {
#                             if (ws && ws.readyState === WebSocket.OPEN) {
#                                 ws.send(reader.result);
#                             }
#                         };
#                         reader.readAsArrayBuffer(blob);
#                     }
#                 }, 'image/jpeg', 0.6);
#             }
#         }

#         function stopSendingFrames() {
#             if (sendFrameIntervalId) {
#                 clearInterval(sendFrameIntervalId);
#                 sendFrameIntervalId = null;
#                 log("Stopped sending frames.");
#             }
#             if (webcamStream) {
#                 webcamStream.getTracks().forEach(track => track.stop());
#                 webcamStream = null;
#                 videoElement.srcObject = null;
#                 log("Webcam stream stopped.");
#             }
#             if (ws) {
#                 if (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING) {
#                     ws.close();
#                 }
#                 ws = null;
#                 log("WebSocket client closed.");
#             }
#         }

#         startButton.addEventListener('click', startStream);
#         stopButton.addEventListener('click', stopSendingFrames);

#         startButton.disabled = false;
#         stopButton.disabled = true;
#     </script>
# </body>
# </html>
# """

# @app.get("/", response_class=HTMLResponse)
# async def get_index():
#     return HTML_CONTENT

# global_processor_running = True

# async def frame_processor_task():
#     global global_processor_running
#     print("[Processor] Thread started, waiting for frames from clients.")
#     process_frame_count = 0
#     process_start_time = time.time()

#     while global_processor_running:
#         async with client_queues_lock:
#             if not client_queues:
#                 await asyncio.sleep(0.01)  # Giảm tải CPU khi không có client
#                 continue

#             for session_id in list(client_queues.keys()):
#                 try:
#                     if session_id not in client_queues:
#                         continue

#                     input_queue = client_queues[session_id]["input"]
#                     output_queue = client_queues[session_id]["output"]
#                     if input_queue.empty():
#                         continue  # Bỏ qua nếu không có frame mới

#                     frame_data_bytes = await asyncio.wait_for(input_queue.get(), timeout=0.01)
                    
#                     np_arr = np.frombuffer(frame_data_bytes, np.uint8)
#                     frame_decoded = cv2.imdecode(np_arr, cv2.IMREAD_COLOR)

#                     if frame_decoded is None:
#                         print(f"[Processor] Could not decode frame for session {session_id}. Skipping.")
#                         continue

#                     processed_frame = process_frame(frame_decoded)
                    
#                     ret_proc, buffer_proc = cv2.imencode('.jpg', processed_frame, [int(cv2.IMWRITE_JPEG_QUALITY), 50])
                    
#                     try:
#                         output_queue.put_nowait(buffer_proc.tobytes())
#                     except asyncio.QueueFull:
#                         pass

#                     process_frame_count += 1
#                     if time.time() - process_start_time >= 1.0:
#                         print(f"[Processor] FPS: {process_frame_count / (time.time() - process_start_time):.2f}")
#                         process_frame_count = 0
#                         process_start_time = time.time()

#                 except asyncio.TimeoutError:
#                     pass
#                 except Exception as e:
#                     print(f"[Processor] Error in frame_processor_task for session {session_id}: {e}")
#                     import traceback
#                     traceback.print_exc()
        
#         await asyncio.sleep(0.001)

# @app.on_event("startup")
# async def startup_event():
#     asyncio.create_task(frame_processor_task())

# @app.websocket("/ws/webcam_stream")
# async def websocket_webcam_stream(websocket: WebSocket):
#     session_id = None
#     try:
#         # Kiểm tra số client hiện tại
#         async with client_queues_lock:
#             if len(client_queues) >= MAX_CLIENTS:
#                 await websocket.close(code=1013, reason="Too many clients connected")
#                 print(f"[WebSocket] Rejected connection: Too many clients (max {MAX_CLIENTS})")
#                 return

#         # Chấp nhận kết nối WebSocket
#         await websocket.accept()
#         session_id = str(uuid.uuid4())
#         print(f"[WebSocket] Client connected: /ws/webcam_stream, session_id: {session_id}")
        
#         # Tạo hàng đợi riêng cho client
#         async with client_queues_lock:
#             client_queues[session_id] = {
#                 "input": asyncio.Queue(maxsize=5),
#                 "output": asyncio.Queue(maxsize=5)
#             }

#         while True:
#             if websocket.client_state != WebSocketState.CONNECTED:
#                 print(f"[WebSocket] Client {session_id} is no longer connected. Breaking loop.")
#                 break

#             try:
#                 frame_bytes_from_client = await asyncio.wait_for(websocket.receive_bytes(), timeout=0.1)
#                 async with client_queues_lock:
#                     if session_id in client_queues:
#                         try:
#                             client_queues[session_id]["input"].put_nowait(frame_bytes_from_client)
#                         except asyncio.QueueFull:
#                             pass
#             except asyncio.TimeoutError:
#                 pass
#             except (WebSocketDisconnect, RuntimeError) as e:
#                 print(f"[WebSocket] Client {session_id} disconnected: {e}")
#                 break
#             except Exception as e:
#                 print(f"[WebSocket] Unexpected error receiving from client {session_id}: {e}")
#                 import traceback
#                 traceback.print_exc()
#                 break

#             try:
#                 if websocket.client_state != WebSocketState.CONNECTED:
#                     print(f"[WebSocket] Client {session_id} is no longer connected. Breaking loop.")
#                     break

#                 async with client_queues_lock:
#                     if session_id in client_queues:
#                         processed_bytes_to_client = await asyncio.wait_for(client_queues[session_id]["output"].get(), timeout=0.01)
#                         await websocket.send_bytes(processed_bytes_to_client)
#             except asyncio.TimeoutError:
#                 pass
#             except (WebSocketDisconnect, RuntimeError) as e:
#                 print(f"[WebSocket] Client {session_id} disconnected while sending: {e}")
#                 break
#             except Exception as e:
#                 print(f"[WebSocket] Unexpected error sending to client {session_id}: {e}")
#                 import traceback
#                 traceback.print_exc()
#                 break

#     except Exception as e:
#         print(f"[WebSocket] Error in websocket_webcam_stream for session {session_id}: {e}")
#         import traceback
#         traceback.print_exc()
#     finally:
#         # Dọn dẹp tài nguyên
#         async with client_queues_lock:
#             if session_id and session_id in client_queues:
#                 del client_queues[session_id]
#                 print(f"[WebSocket] Cleaned up queues for session {session_id}")
        
#         if websocket.client_state == WebSocketState.CONNECTED:
#             try:
#                 await websocket.close(code=1000, reason="Normal closure")
#                 print(f"[WebSocket] Closed WebSocket for session {session_id}")
#             except Exception as e:
#                 print(f"[WebSocket] Error closing WebSocket for session {session_id}: {e}")

# @app.on_event("shutdown")
# def shutdown_event():
#     global global_processor_running
#     global_processor_running = False
#     print("FastAPI application shutting down.")

# from pyngrok import ngrok
# if __name__ == "__main__":
#     public_url = ngrok.connect(8000, bind_tls=True).public_url
#     print("Public URL:", public_url)
#     uvicorn.run(app, host="0.0.0.0", port=8000)



# import cv2
# import uvicorn
# from fastapi import FastAPI, WebSocket, WebSocketDisconnect
# from fastapi.responses import HTMLResponse
# from fastapi.staticfiles import StaticFiles
# import asyncio
# import numpy as np
# import uuid
# import time
# from starlette.websockets import WebSocketState
# import collections # Dùng để giữ lịch sử frames của client

# try:
#     from yolo_processor import process_frame # Sử dụng phiên bản đã tối ưu
# except ImportError:
#     print("Error: yolo_processor.py not found. Please ensure it's in the same directory.")
#     exit()

# app = FastAPI()

# # Mount static files (ensure you have a 'static' directory with styles.css if needed)
# app.mount("/static", StaticFiles(directory="static"), name="static")

# # Dictionary to hold input/output queues for each client
# # We'll use a deque for input to easily drop older frames
# client_queues = {}
# client_queues_lock = asyncio.Lock()
# MAX_CLIENTS = 4 # Tăng giới hạn client nếu GPU của bạn đủ mạnh
# BATCH_SIZE = 10 # Kích thước batch cho YOLO. Tùy chỉnh dựa trên GPU và hiệu suất.
# MAX_QUEUE_SIZE_INPUT = 2 # Kích thước tối đa của hàng đợi input cho mỗi client (ví dụ: 2-3 frames)
# MAX_QUEUE_SIZE_OUTPUT = 2 # Kích thước tối đa của hàng đợi output cho mỗi client (ví dụ: 1-2 frames)

# HTML_CONTENT = """
# <!DOCTYPE html>
# <html>
# <head>
#     <title>Webcam Stream with YOLOv8 (Client-side)</title>
#     <style>
#         body { font-family: Arial, sans-serif; display: flex; flex-direction: column; align-items: center; background-color: #f0f0f0; margin: 0; padding: 20px; }
#         h1 { color: #333; }
#         .video-container { display: flex; gap: 20px; margin-top: 20px; }
#         video, img { border: 2px solid #ccc; background-color: #eee; }
#         .controls { margin-top: 20px; }
#         button { padding: 10px 20px; font-size: 16px; cursor: pointer; }
#         .log-container { margin-top: 20px; background-color: #fff; padding: 10px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); width: 80%; max-width: 1200px; height: 150px; overflow-y: scroll; border: 1px solid #ddd;}
#         pre { white-space: pre-wrap; word-wrap: break-word; }
#     </style>
#     <link rel="stylesheet" href="/static/styles.css"> 
# </head>
# <body>
#     <h1>Real-Time Object Detection (Client Webcam)</h1>
#     <div class="video-container">
#         <div>
#             <h2>Your Webcam (Raw)</h2>
#             <video id="webcamVideo" width="640" height="480" autoplay muted></video>
#         </div>
#         <div>
#             <h2>Processed Output (YOLOv8)</h2>
#             <img id="processedImage" width="640" height="480">
#         </div>
#         <canvas id="webcamCanvas" width="640" height="480" style="display: none;"></canvas>
#     </div>
#     <div class="controls">
#         <button id="startButton">Start Stream</button>
#         <button id="stopButton">Stop Stream</button>
#     </div>
#     <div class="log-container">
#         <pre id="log"></pre>
#     </div>

#     <script>
#         const videoElement = document.getElementById('webcamVideo');
#         const canvasElement = document.getElementById('webcamCanvas');
#         const processedImageElement = document.getElementById('processedImage');
#         const startButton = document.getElementById('startButton');
#         const stopButton = document.getElementById('stopButton');
#         const logElement = document.getElementById('log');
#         const canvasContext = canvasElement.getContext('2d');
#         let ws;
#         let webcamStream;
#         let sendFrameIntervalId;
#         const targetFPS = 30; // Aim for 30 frames per second
#         const sendFrameRate = 1000 / targetFPS; // Milliseconds per frame

#         function log(message) {
#             const now = new Date().toLocaleTimeString();
#             logElement.textContent += `[${now}] ${message}\n`;
#             logElement.scrollTop = logElement.scrollHeight;
#         }

#         function startStream() {
#             if (ws && ws.readyState === WebSocket.OPEN) {
#                 log("Stream already active.");
#                 return;
#             }

#             if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
#                 log("WebRTC is not supported in this browser.");
#                 alert("Your browser does not support WebRTC. Please use a modern browser like Chrome or Firefox.");
#                 return;
#             }

#             log("Attempting to access webcam...");
#             navigator.mediaDevices.getUserMedia({ 
#                 video: {
#                     width: { ideal: 640 }, // Request ideal resolution
#                     height: { ideal: 480 },
#                     frameRate: { ideal: targetFPS } // Request ideal frame rate from webcam
#                 }
#             })
#             .then(function(stream) {
#                 webcamStream = stream;
#                 videoElement.srcObject = stream;
#                 videoElement.onloadedmetadata = () => {
#                     log("Webcam stream started successfully.");
#                     // Adjust canvas and image size to video intrinsic size for better fit
#                     canvasElement.width = videoElement.videoWidth;
#                     canvasElement.height = videoElement.videoHeight;
#                     processedImageElement.width = videoElement.videoWidth;
#                     processedImageElement.height = videoElement.videoHeight;
                    
#                     // Display actual webcam resolution
#                     log(`Webcam resolution: ${videoElement.videoWidth}x${videoElement.videoHeight}`);
#                 };

#                 const publicUrl = window.location.hostname;
#                 const wsProtocol = window.location.protocol === "https:" ? "wss:" : "ws:";
#                 const wsUrl = `${wsProtocol}//${publicUrl}/ws/webcam_stream`;
#                 log(`Connecting to WebSocket at ${wsUrl}...`);
#                 ws = new WebSocket(wsUrl);
#                 ws.binaryType = 'arraybuffer';

#                 ws.onopen = function() {
#                     log("WebSocket connected. Starting to send frames...");
#                     sendFrameIntervalId = setInterval(sendFrame, sendFrameRate);
#                     startButton.disabled = true;
#                     stopButton.disabled = false;
#                 };

#                 ws.onmessage = function(event) {
#                     const blob = new Blob([event.data], {type: 'image/jpeg'});
#                     const url = URL.createObjectURL(blob);
#                     // Revoke previous object URL to avoid memory leaks
#                     if (processedImageElement.src && processedImageElement.src.startsWith('blob:')) {
#                         URL.revokeObjectURL(processedImageElement.src);
#                     }
#                     processedImageElement.src = url;
#                 };

#                 ws.onclose = function(event) {
#                     log("WebSocket disconnected. Code: " + event.code + ", Reason: " + event.reason);
#                     stopSendingFrames(); // Ensure everything is cleaned up
#                     startButton.disabled = false;
#                     stopButton.disabled = true;
#                 };

#                 ws.onerror = function(error) {
#                     log("WebSocket error: " + (error.message || error));
#                     console.error("WebSocket error:", error);
#                     ws.close(); // Close connection on error
#                 };

#             })
#             .catch(function(error) {
#                 log("Error accessing webcam: " + error.name + " - " + error.message);
#                 console.error("Error accessing webcam: ", error);
#                 alert("Could not access webcam. Please ensure it's connected and you've granted permission.");
#             });
#         }

#         function sendFrame() {
#             // Check if WebSocket is open and video element is ready
#             if (ws && ws.readyState === WebSocket.OPEN && videoElement.videoWidth > 0 && videoElement.readyState >= 2) {
#                 canvasContext.drawImage(videoElement, 0, 0, canvasElement.width, canvasElement.height);
#                 canvasElement.toBlob(function(blob) {
#                     if (blob) {
#                         const reader = new FileReader();
#                         reader.onload = function() {
#                             if (ws && ws.readyState === WebSocket.OPEN) {
#                                 ws.send(reader.result);
#                             }
#                         };
#                         reader.readAsArrayBuffer(blob);
#                     }
#                 }, 'image/jpeg', 0.7); // Tăng chất lượng JPEG lên 70%
#             }
#         }

#         function stopSendingFrames() {
#             if (sendFrameIntervalId) {
#                 clearInterval(sendFrameIntervalId);
#                 sendFrameIntervalId = null;
#                 log("Stopped sending frames.");
#             }
#             if (webcamStream) {
#                 webcamStream.getTracks().forEach(track => track.stop());
#                 webcamStream = null;
#                 videoElement.srcObject = null;
#                 log("Webcam stream stopped.");
#             }
#             if (ws) {
#                 if (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING) {
#                     ws.close();
#                 }
#                 ws = null;
#                 log("WebSocket client closed.");
#             }
#         }

#         startButton.addEventListener('click', startStream);
#         stopButton.addEventListener('click', stopSendingFrames);

#         // Initial state of buttons
#         startButton.disabled = false;
#         stopButton.disabled = true;
#     </script>
# </body>
# </html>
# """

# @app.get("/", response_class=HTMLResponse)
# async def get_index():
#     return HTML_CONTENT

# global_processor_running = True

# async def frame_processor_task():
#     global global_processor_running
#     print("[Processor] Thread started, waiting for frames from clients.")
#     process_frame_count = 0
#     process_start_time = time.time()

#     while global_processor_running:
#         frames_to_process = []
#         client_session_ids = []

#         async with client_queues_lock:
#             # Collect frames from all active clients up to BATCH_SIZE
#             for session_id in list(client_queues.keys()):
#                 input_queue = client_queues[session_id]["input"]
#                 if not input_queue.empty():
#                     # Try to get the latest frame. If queue is full, skip older frames.
#                     # This helps reduce latency by always processing the freshest data.
#                     while input_queue.qsize() > MAX_QUEUE_SIZE_INPUT:
#                         await input_queue.get() # Discard older frames

#                     try:
#                         frame_data_bytes = await asyncio.wait_for(input_queue.get(), timeout=0.001) # Short timeout
#                         np_arr = np.frombuffer(frame_data_bytes, np.uint8)
#                         frame_decoded = cv2.imdecode(np_arr, cv2.IMREAD_COLOR)

#                         if frame_decoded is not None:
#                             frames_to_process.append(frame_decoded)
#                             client_session_ids.append(session_id)
#                         else:
#                             print(f"[Processor] Could not decode frame for session {session_id}. Skipping.")
#                     except asyncio.TimeoutError:
#                         pass # No new frame, continue to next client
#                     except Exception as e:
#                         print(f"[Processor] Error getting frame from input queue for session {session_id}: {e}")
#                         import traceback
#                         traceback.print_exc()

#         if frames_to_process:
#             try:
#                 # Process the batch of frames
#                 processed_frames_batch = process_frame(frames_to_process)
                
#                 for idx, processed_frame in enumerate(processed_frames_batch):
#                     session_id = client_session_ids[idx]
                    
#                     if session_id in client_queues: # Check if client is still connected
#                         output_queue = client_queues[session_id]["output"]
#                         ret_proc, buffer_proc = cv2.imencode('.jpg', processed_frame, [int(cv2.IMWRITE_JPEG_QUALITY), 70]) # Tăng chất lượng ảnh gửi về

#                         if ret_proc:
#                             try:
#                                 # If output queue is full, discard the older processed frame to send the latest one
#                                 while output_queue.qsize() >= MAX_QUEUE_SIZE_OUTPUT:
#                                     output_queue.get_nowait()
#                                 output_queue.put_nowait(buffer_proc.tobytes())
#                             except asyncio.QueueFull:
#                                 pass # This should be rare with the `while` loop above
#                             except Exception as e:
#                                 print(f"[Processor] Error putting frame to output queue for session {session_id}: {e}")
#                         else:
#                             print(f"[Processor] Could not encode processed frame for session {session_id}.")

#                 process_frame_count += len(frames_to_process)
#                 if time.time() - process_start_time >= 1.0:
#                     print(f"[Processor] Processed {process_frame_count} frames in 1s. FPS: {process_frame_count / (time.time() - process_start_time):.2f}")
#                     process_frame_count = 0
#                     process_start_time = time.time()
#             except Exception as e:
#                 print(f"[Processor] Error processing batch: {e}")
#                 import traceback
#                 traceback.print_exc()

#         await asyncio.sleep(0.001) # Small sleep to yield control and prevent tight loop

# @app.on_event("startup")
# async def startup_event():
#     # Start the frame processing task in the background
#     asyncio.create_task(frame_processor_task())

# @app.websocket("/ws/webcam_stream")
# async def websocket_webcam_stream(websocket: WebSocket):
#     session_id = None
#     try:
#         async with client_queues_lock:
#             if len(client_queues) >= MAX_CLIENTS:
#                 await websocket.close(code=1013, reason=f"Too many clients connected. Max: {MAX_CLIENTS}")
#                 print(f"[WebSocket] Rejected connection: Too many clients (max {MAX_CLIENTS})")
#                 return

#         await websocket.accept()
#         session_id = str(uuid.uuid4())
#         print(f"[WebSocket] Client connected: /ws/webcam_stream, session_id: {session_id}")
        
#         async with client_queues_lock:
#             # Using deque to allow discarding old elements easily
#             client_queues[session_id] = {
#                 "input": asyncio.Queue(maxsize=MAX_QUEUE_SIZE_INPUT), 
#                 "output": asyncio.Queue(maxsize=MAX_QUEUE_SIZE_OUTPUT)
#             }

#         # Create separate tasks for receiving and sending to avoid blocking each other
#         receive_task = asyncio.create_task(receive_frames(websocket, session_id))
#         send_task = asyncio.create_task(send_processed_frames(websocket, session_id))

#         await asyncio.gather(receive_task, send_task)

#     except WebSocketDisconnect as e:
#         print(f"[WebSocket] Client {session_id} disconnected: {e.code} - {e.reason}")
#     except RuntimeError as e:
#         print(f"[WebSocket] Runtime error for client {session_id}: {e}")
#     except Exception as e:
#         print(f"[WebSocket] Unhandled error in websocket_webcam_stream for session {session_id}: {e}")
#         import traceback
#         traceback.print_exc()
#     finally:
#         # Clean up resources associated with the disconnected client
#         async with client_queues_lock:
#             if session_id and session_id in client_queues:
#                 del client_queues[session_id]
#                 print(f"[WebSocket] Cleaned up queues for session {session_id}")
        
#         if websocket.client_state == WebSocketState.CONNECTED:
#             try:
#                 await websocket.close(code=1000, reason="Normal closure")
#                 print(f"[WebSocket] Closed WebSocket for session {session_id}")
#             except Exception as e:
#                 print(f"[WebSocket] Error closing WebSocket for session {session_id}: {e}")

# async def receive_frames(websocket: WebSocket, session_id: str):
#     while websocket.client_state == WebSocketState.CONNECTED:
#         try:
#             frame_bytes_from_client = await asyncio.wait_for(websocket.receive_bytes(), timeout=0.05) # Giảm timeout
#             async with client_queues_lock:
#                 if session_id in client_queues:
#                     input_queue = client_queues[session_id]["input"]
#                     try:
#                         # Drop old frames if queue is full to prioritize fresh data
#                         if input_queue.qsize() >= MAX_QUEUE_SIZE_INPUT:
#                             await input_queue.get_nowait() # Discard the oldest frame
#                         input_queue.put_nowait(frame_bytes_from_client)
#                     except asyncio.QueueFull: # Should not happen often with the discard logic
#                         pass 
#                     except Exception as e:
#                         print(f"[Receive Task] Error putting frame to input queue for session {session_id}: {e}")
#                         break # Exit loop on unexpected queue error
#         except asyncio.TimeoutError:
#             pass # No frame received within timeout, just continue
#         except (WebSocketDisconnect, RuntimeError) as e:
#             print(f"[Receive Task] Client {session_id} disconnected or runtime error during receive: {e}")
#             break
#         except Exception as e:
#             print(f"[Receive Task] Unexpected error receiving from client {session_id}: {e}")
#             import traceback
#             traceback.print_exc()
#             break

# async def send_processed_frames(websocket: WebSocket, session_id: str):
#     while websocket.client_state == WebSocketState.CONNECTED:
#         try:
#             async with client_queues_lock:
#                 if session_id in client_queues:
#                     output_queue = client_queues[session_id]["output"]
#                     processed_bytes_to_client = await asyncio.wait_for(output_queue.get(), timeout=0.05) # Giảm timeout
#                     await websocket.send_bytes(processed_bytes_to_client)
#         except asyncio.TimeoutError:
#             pass # No processed frame ready, just continue
#         except (WebSocketDisconnect, RuntimeError) as e:
#             print(f"[Send Task] Client {session_id} disconnected or runtime error during send: {e}")
#             break
#         except Exception as e:
#             print(f"[Send Task] Unexpected error sending to client {session_id}: {e}")
#             import traceback
#             traceback.print_exc()
#             break

# @app.on_event("shutdown")
# def shutdown_event():
#     global global_processor_running
#     global_processor_running = False
#     print("FastAPI application shutting down.")

# # ngrok for local testing. In production, deploy directly.
# from pyngrok import ngrok
# if __name__ == "__main__":
#     public_url = ngrok.connect(8000, bind_tls=True).public_url
#     print("Public URL (ngrok):", public_url)
#     uvicorn.run(app, host="0.0.0.0", port=8000)


import cv2
import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
import asyncio
import numpy as np
import uuid
import time
from starlette.websockets import WebSocketState
import csv
import os
import csv
from yolo_processor import YOLOProcessor
from attendance_tracker import AttendanceTracker
import threading




class WebcamStreamer:
    """
    A class to manage a real-time webcam streaming server with YOLOv8 object detection.
    Loads YOLO model from a local file without downloading.
    """

    def __init__(self, host="127.0.0.1", port=8000, max_clients=4, batch_size=10, 
                 max_queue_size_input=2, max_queue_size_output=2, model_path='models/yolov8n.pt'):
        """
        Initialize the WebcamStreamer with configuration parameters.

        Args:
            host (str): Host address for the FastAPI server.
            port (int): Port for the FastAPI server.
            max_clients (int): Maximum number of concurrent clients.
            batch_size (int): Batch size for YOLO processing.
            max_queue_size_input (int): Maximum size of input queue per client.
            max_queue_size_output (int): Maximum size of output queue per client.
            model_path (str): Path to the local YOLO model file (e.g., 'models/yolov8n.pt').
        """
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"YOLO model file not found at {model_path}. Please ensure the file exists.")
        
        self.app = FastAPI()
        self.host = host
        self.port = port
        self.max_clients = max_clients
        self.batch_size = batch_size
        self.max_queue_size_input = max_queue_size_input
        self.max_queue_size_output = max_queue_size_output
        self.client_queues = {}
        self.client_queues_lock = asyncio.Lock()
        self.global_processor_running = True
        self.yolo_processor = YOLOProcessor(model_path=model_path)
        self.attendance_tracker = AttendanceTracker()

        # Add CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

        # Mount static files
        self.app.mount("/static", StaticFiles(directory="static"), name="static")

        # Define HTML content with dynamic WebSocket URL and enhanced error logging
        self.HTML_CONTENT = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Webcam Stream with YOLOv8 (Client-side)</title>
            <style>
                body { font-family: Arial, sans-serif; display: flex; flex-direction: column; align-items: center; background-color: #f0f0f0; margin: 0; padding: 20px; }
                h1 { color: #333; }
                .main-container { display: flex; gap: 20px; margin-top: 20px; width: 100%; max-width: 1400px; }
                .video-section { flex: 2; }
                .video-container { display: flex; gap: 20px; }
                video, img { border: 2px solid #ccc; background-color: #eee; }
                .attendance-section { flex: 1; min-width: 300px; }
                .attendance-container { background-color: #fff; padding: 15px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); border: 1px solid #ddd; }
                .attendance-table { width: 100%; border-collapse: collapse; margin-top: 10px; }
                .attendance-table th, .attendance-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                .attendance-table th { background-color: #f2f2f2; font-weight: bold; }
                .attendance-table tr:nth-child(even) { background-color: #f9f9f9; }
                .controls { margin-top: 20px; }
                button { padding: 10px 20px; font-size: 16px; cursor: pointer; }
                .log-container { margin-top: 20px; background-color: #fff; padding: 10px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); width: 80%; max-width: 1200px; height: 150px; overflow-y: scroll; border: 1px solid #ddd;}
                pre { white-space: pre-wrap; word-wrap: break-word; }
                .refresh-btn { background-color: #4CAF50; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-left: 10px; }
                .refresh-btn:hover { background-color: #45a049; }
            </style>
            <link rel="stylesheet" href="/static/styles.css"> 
        </head>
        <body>
            <h1>Real-Time Object Detection (Client Webcam)</h1>
            <div class="main-container">
                <div class="video-section">
                    <div class="video-container">
                        <div>
                            <h2>Your Webcam (Raw)</h2>
                            <video id="webcamVideo" width="640" height="480" autoplay muted></video>
                        </div>
                        <div>
                            <h2>Processed Output (YOLOv8)</h2>
                            <img id="processedImage" width="640" height="480">
                        </div>
                        <canvas id="webcamCanvas" width="640" height="480" style="display: none;"></canvas>
                    </div>
                    <div class="controls">
                        <button id="startButton">Start Stream</button>
                        <button id="stopButton">Stop Stream</button>
                    </div>
                </div>
                <div class="attendance-section">
                    <div class="attendance-container">
                        <h2>Attendance Log <button class="refresh-btn" onclick="refreshAttendance()">Refresh</button></h2>
                        <table class="attendance-table" id="attendanceTable">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Timestamp</th>
                                </tr>
                            </thead>
                            <tbody id="attendanceBody">
                                <!-- Attendance records will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="log-container">
                <pre id="log"></pre>
            </div>

            <script>
                const videoElement = document.getElementById('webcamVideo');
                const canvasElement = document.getElementById('webcamCanvas');
                const processedImageElement = document.getElementById('processedImage');
                const startButton = document.getElementById('startButton');
                const stopButton = document.getElementById('stopButton');
                const logElement = document.getElementById('log');
                const canvasContext = canvasElement.getContext('2d');
                let ws;
                let webcamStream;
                let sendFrameIntervalId;
                const targetFPS = 30;
                const sendFrameRate = 1000 / targetFPS;

                function log(message) {
                    const now = new Date().toLocaleTimeString();
                    logElement.textContent += `[${now}] ${message}\n`;
                    logElement.scrollTop = logElement.scrollHeight;
                }

                function startStream() {
                    log("Starting stream...");
                    if (ws && ws.readyState === WebSocket.OPEN) {
                        log("Stream already active.");
                        return;
                    }

                    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                        log("WebRTC not supported.");
                        alert("WebRTC not supported.");
                        return;
                    }

                    log("Requesting webcam access...");
                    navigator.mediaDevices.getUserMedia({ 
                        video: {
                            width: { ideal: 640 },
                            height: { ideal: 480 },
                            frameRate: { ideal: targetFPS }
                        }
                    })
                    .then(function(stream) {
                        log("Webcam access granted.");
                        webcamStream = stream;
                        videoElement.srcObject = stream;
                        videoElement.onloadedmetadata = () => {
                            log(`Webcam resolution: ${videoElement.videoWidth}x${videoElement.videoHeight}`);
                            canvasElement.width = videoElement.videoWidth;
                            canvasElement.height = videoElement.videoHeight;
                            processedImageElement.width = videoElement.videoWidth;
                            processedImageElement.height = videoElement.videoHeight;
                        };

                        const wsProtocol = window.location.protocol === "https:" ? "wss:" : "ws:";
                        const wsHost = window.location.host; // Use host to include port if non-standard
                        const wsUrl = `${wsProtocol}//${wsHost}/ws/webcam_stream`;
                        log(`Connecting to WebSocket at ${wsUrl}...`);
                        
                        try {
                            ws = new WebSocket(wsUrl);
                            ws.binaryType = 'arraybuffer';

                            ws.onopen = () => {
                                log("WebSocket connected.");
                                sendFrameIntervalId = setInterval(sendFrame, sendFrameRate);
                                startButton.disabled = true;
                                stopButton.disabled = false;
                            };

                            ws.onmessage = (event) => {
                                log("Received processed frame.");
                                const blob = new Blob([event.data], { type: 'image/jpeg' });
                                const url = URL.createObjectURL(blob);
                                if (processedImageElement.src && processedImageElement.src.startsWith('blob:')) {
                                    URL.revokeObjectURL(processedImageElement.src);
                                }
                                processedImageElement.src = url;
                            };

                            ws.onclose = (event) => {
                                log(`WebSocket closed. Code: ${event.code}, Reason: ${event.reason}`);
                                stopSendingFrames();
                                startButton.disabled = false;
                                stopButton.disabled = true;
                            };

                            ws.onerror = (error) => {
                                log("WebSocket error: " + (error.message || error));
                                console.error("WebSocket error:", error);
                                ws.close();
                            };
                        } catch (e) {
                            log(`WebSocket initialization failed: ${e.message}`);
                            console.error("WebSocket init error:", e);
                        }
                    })
                    .catch(function(error) {
                        log(`Webcam access failed: ${error.name} - ${error.message}`);
                        console.error("Webcam error:", error);
                        alert(`Webcam access failed: ${error.name} - ${error.message}`);
                    });
                }

                function sendFrame() {
                    if (ws && ws.readyState === WebSocket.OPEN && videoElement.videoWidth > 0 && videoElement.readyState >= 2) {
                        canvasContext.drawImage(videoElement, 0, 0, canvasElement.width, canvasElement.height);
                        canvasElement.toBlob(function(blob) {
                            if (blob) {
                                const reader = new FileReader();
                                reader.onload = function() {
                                    if (ws && ws.readyState === WebSocket.OPEN) {
                                        ws.send(reader.result);
                                    }
                                };
                                reader.onerror = function(error) {
                                    log(`Error reading blob: ${error}`);
                                };
                                reader.readAsArrayBuffer(blob);
                            }
                        }, 'image/jpeg', 0.7);
                    }
                }

                function stopSendingFrames() {
                    if (sendFrameIntervalId) {
                        clearInterval(sendFrameIntervalId);
                        sendFrameIntervalId = null;
                        log("Stopped sending frames.");
                    }
                    if (webcamStream) {
                        webcamStream.getTracks().forEach(track => track.stop());
                        webcamStream = null;
                        videoElement.srcObject = null;
                        log("Webcam stream stopped.");
                    }
                    if (ws) {
                        if (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING) {
                            ws.close();
                        }
                        ws = null;
                        log("WebSocket client closed.");
                    }
                }

                startButton.addEventListener('click', startStream);
                stopButton.addEventListener('click', stopSendingFrames);
                startButton.disabled = false;
                stopButton.disabled = true;

                // Attendance management functions
                function refreshAttendance() {
                    fetch('/api/attendance')
                        .then(response => response.json())
                        .then(data => {
                            updateAttendanceTable(data.attendance);
                        })
                        .catch(error => {
                            console.error('Error fetching attendance:', error);
                            log('Error fetching attendance data');
                        });
                }

                function updateAttendanceTable(attendanceData) {
                    const tbody = document.getElementById('attendanceBody');
                    tbody.innerHTML = '';

                    if (attendanceData.length === 0) {
                        const row = tbody.insertRow();
                        const cell = row.insertCell(0);
                        cell.colSpan = 2;
                        cell.textContent = 'No attendance records yet';
                        cell.style.textAlign = 'center';
                        cell.style.fontStyle = 'italic';
                        cell.style.color = '#666';
                    } else {
                        attendanceData.forEach(record => {
                            const row = tbody.insertRow();
                            const nameCell = row.insertCell(0);
                            const timeCell = row.insertCell(1);
                            nameCell.textContent = record.name;
                            timeCell.textContent = record.timestamp;
                        });
                    }
                }

                // Auto-refresh attendance every 5 seconds
                setInterval(refreshAttendance, 5000);

                // Initial load of attendance data
                refreshAttendance();
            </script>
        </body>
        </html>
        """

        # Setup routes and event handlers
        self._setup_routes()

    def _setup_routes(self):
        """Setup FastAPI routes and WebSocket endpoints."""
        @self.app.get("/", response_class=HTMLResponse)
        async def get_index():
            return self.HTML_CONTENT

        @self.app.get("/api/attendance")
        async def get_attendance():
            """API endpoint to get current attendance log."""
            return {"attendance": self.attendance_tracker.get_attendance_log()}

        @self.app.get("/api/attendance/csv")
        async def download_attendance_csv():
            """API endpoint to download attendance log as CSV."""
            from fastapi.responses import FileResponse
            csv_path = self.attendance_tracker.export_to_csv()
            return FileResponse(csv_path, media_type='text/csv', filename='attendance_log.csv')

        @self.app.on_event("startup")
        async def startup_event():
            asyncio.create_task(self._frame_processor_task())

        @self.app.on_event("shutdown")
        async def shutdown_event():
            self.global_processor_running = False
            print("FastAPI application shutting down.")

        @self.app.websocket("/ws/webcam_stream")
        async def websocket_webcam_stream(websocket: WebSocket):
            session_id = None
            receive_task = None
            send_task = None
            try:
                async with self.client_queues_lock:
                    if len(self.client_queues) >= self.max_clients:
                        await websocket.close(code=1013, reason=f"Quá nhiều client: {self.max_clients}")
                        return
                await websocket.accept()
                session_id = str(uuid.uuid4())
                print(f"[WebSocket] Client kết nối: /ws/webcam_stream, session_id: {session_id}")

                async with self.client_queues_lock:
                    self.client_queues[session_id] = {
                        "input": asyncio.Queue(maxsize=self.max_queue_size_input),
                        "output": asyncio.Queue(maxsize=self.max_queue_size_output)
                    }

                receive_task = asyncio.create_task(self._receive_frames(websocket, session_id))
                send_task = asyncio.create_task(self._send_processed_frames(websocket, session_id))
                await asyncio.gather(receive_task, send_task)

            except WebSocketDisconnect as e:
                print(f"[WebSocket] Client {session_id} ngắt kết nối: {e.code} - {e.reason}")
            finally:
                async with self.client_queues_lock:
                    if session_id in self.client_queues:
                        del self.client_queues[session_id]
                        print(f"[WebSocket] Dọn dẹp hàng đợi cho session {session_id}")
                if receive_task:
                    receive_task.cancel()
                if send_task:
                    send_task.cancel()
                if websocket.client_state == WebSocketState.CONNECTED:
                    try:
                        await websocket.close(code=1000, reason="Đóng bình thường")
                    except Exception as e:
                        print(f"[WebSocket] Lỗi đóng WebSocket cho session {session_id}: {e}")

    async def _frame_processor_task(self):
        """Background task to process frames from all clients using YOLOProcessor."""
        print("[Processor] Thread started, waiting for frames from clients.")
        process_frame_count = 0
        process_start_time = time.time()

        while self.global_processor_running:
            frames_to_process = []
            client_session_ids = []

            async with self.client_queues_lock:
                for session_id in list(self.client_queues.keys()):
                    input_queue = self.client_queues[session_id]["input"]
                    if not input_queue.empty():
                        while input_queue.qsize() > self.max_queue_size_input:
                            await input_queue.get()

                        try:
                            frame_data_bytes = await asyncio.wait_for(input_queue.get(), timeout=0.001)
                            np_arr = np.frombuffer(frame_data_bytes, np.uint8)
                            frame_decoded = cv2.imdecode(np_arr, cv2.IMREAD_COLOR)

                            if frame_decoded is not None:
                                frames_to_process.append(frame_decoded)
                                client_session_ids.append(session_id)
                            else:
                                print(f"[Processor] Could not decode frame for session {session_id}. Skipping.")
                        except asyncio.TimeoutError:
                            pass
                        except Exception as e:
                            print(f"[Processor] Error getting frame for session {session_id}: {e}")
                            import traceback
                            traceback.print_exc()

            if frames_to_process:
                try:
                    # Process frames and get both processed frames and detections
                    processed_frames_batch, detections_batch = self.yolo_processor.process_frames(frames_to_process, return_detections=True)

                    for idx, processed_frame in enumerate(processed_frames_batch):
                        session_id = client_session_ids[idx]

                        # Update attendance tracking with detections
                        if idx < len(detections_batch):
                            detections = detections_batch[idx]
                            self.attendance_tracker.track_objects(detections, session_id)

                        if session_id in self.client_queues:
                            output_queue = self.client_queues[session_id]["output"]
                            ret_proc, buffer_proc = cv2.imencode('.jpg', processed_frame, [int(cv2.IMWRITE_JPEG_QUALITY), 70])

                            if ret_proc:
                                try:
                                    while output_queue.qsize() >= self.max_queue_size_output:
                                        output_queue.get_nowait()
                                    output_queue.put_nowait(buffer_proc.tobytes())
                                except asyncio.QueueFull:
                                    pass
                                except Exception as e:
                                    print(f"[Processor] Error putting frame to output queue for session {session_id}: {e}")
                            else:
                                print(f"[Processor] Could not encode processed frame for session {session_id}.")

                    process_frame_count += len(frames_to_process)
                    if time.time() - process_start_time >= 1.0:
                        print(f"[Processor] Processed {process_frame_count} frames in 1s. FPS: {process_frame_count / (time.time() - process_start_time):.2f}")
                        process_frame_count = 0
                        process_start_time = time.time()
                except Exception as e:
                    print(f"[Processor] Error processing batch: {e}")
                    import traceback
                    traceback.print_exc()

            await asyncio.sleep(0.001)

    async def _receive_frames(self, websocket: WebSocket, session_id: str):
        """Handle receiving frames from a client."""
        while websocket.client_state == WebSocketState.CONNECTED:
            try:
                frame_bytes_from_client = await asyncio.wait_for(websocket.receive_bytes(), timeout=0.05)
                async with self.client_queues_lock:
                    if session_id in self.client_queues:
                        input_queue = self.client_queues[session_id]["input"]
                        try:
                            if input_queue.qsize() >= self.max_queue_size_input:
                                await input_queue.get_nowait()
                            input_queue.put_nowait(frame_bytes_from_client)
                        except asyncio.QueueFull:
                            pass
                        except Exception as e:
                            print(f"[Receive Task] Error putting frame to input queue for session {session_id}: {e}")
                            break
            except asyncio.TimeoutError:
                pass
            except (WebSocketDisconnect, RuntimeError) as e:
                print(f"[Receive Task] Client {session_id} disconnected or runtime error: {e}")
                break
            except Exception as e:
                print(f"[Receive Task] Unexpected error receiving from client {session_id}: {e}")
                import traceback
                traceback.print_exc()
                break

    async def _send_processed_frames(self, websocket: WebSocket, session_id: str):
        """Handle sending processed frames to a client."""
        while websocket.client_state == WebSocketState.CONNECTED:
            try:
                async with self.client_queues_lock:
                    if session_id in self.client_queues:
                        output_queue = self.client_queues[session_id]["output"]
                        processed_bytes_to_client = await asyncio.wait_for(output_queue.get(), timeout=0.05)
                        await websocket.send_bytes(processed_bytes_to_client)
            except asyncio.TimeoutError:
                pass
            except (WebSocketDisconnect, RuntimeError) as e:
                print(f"[Send Task] Client {session_id} disconnected or runtime error: {e}")
                break
            except Exception as e:
                print(f"[Send Task] Unexpected error sending to client {session_id}: {e}")
                import traceback
                traceback.print_exc()
                break

    def run(self):
        config = uvicorn.Config(self.app, host=self.host, port=self.port)
        server = uvicorn.Server(config)
        local_url = f"http://{self.host}:{self.port}"
        public_url = local_url  # Mặc định public_url là local_url nếu không có ngrok
        try:
            from pyngrok import ngrok
            public_url = ngrok.connect(self.port, bind_tls=True).public_url
            print("FastAPI Public URL (ngrok):", public_url)
        except ImportError:
            print("Không cài ngrok, chạy FastAPI cục bộ tại:", local_url)

        uvicorn_thread = threading.Thread(target=server.run)
        uvicorn_thread.daemon = True
        uvicorn_thread.start()
        time.sleep(1)
        return public_url, local_url

if __name__ == "__main__":
    streamer = WebcamStreamer()
    streamer.run()