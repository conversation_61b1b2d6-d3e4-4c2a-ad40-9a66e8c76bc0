/* General body styling with dynamic dark green-black to blue-black theme */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #0a1f17 0%, #0a0a0a 50%, #0a1726 100%);
    color: #a0d8c8;
    margin: 0;
    padding: 30px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow-x: hidden;
    animation: dynamicBackground 12s ease-in-out infinite alternate;
}

/* Dynamic multi-color background animation */
@keyframes dynamicBackground {
    0% { background: linear-gradient(135deg, #0a1f17 0%, #0a0a0a 50%, #0a1726 100%); }
    33% { background: linear-gradient(135deg, #0f2a1e 0%, #0a0a0a 50%, #1a2e3b 100%); }
    66% { background: linear-gradient(135deg, #0a1726 0%, #0a0a0a 50%, #0f3b4a 100%); }
    100% { background: linear-gradient(135deg, #0a1f17 0%, #0a0a0a 50%, #0a1726 100%); }
}

/* Heading styling with subtle glow */
h1 {
    color: #d0f0e6;
    text-shadow: 0 0 10px rgba(0, 200, 150, 0.5), 0 2px 5px rgba(0, 0, 0, 0.6);
    font-size: 3rem;
    margin-bottom: 2.2rem;
    text-align: center;
    letter-spacing: 1.2px;
    animation: glowPulse 2.5s ease-in-out infinite alternate;
}

h2 {
    color: #70c0b0;
    font-size: 1.7rem;
    margin-bottom: 1.3rem;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.8px;
}

/* Glow pulse animation for headings */
@keyframes glowPulse {
    0% { text-shadow: 0 0 10px rgba(0, 200, 150, 0.5), 0 2px 5px rgba(0, 0, 0, 0.6); }
    100% { text-shadow: 0 0 15px rgba(0, 200, 150, 0.7), 0 2px 5px rgba(0, 0, 0, 0.8); }
}

/* Video container with a clean, dark layout (no shimmer) */
.video-container {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    justify-content: center;
    margin-top: 2.5rem;
    width: 100%;
    max-width: 1600px;
    padding: 25px;
    background: rgba(10, 10, 20, 0.5);
    border-radius: 18px;
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.8);
}

/* Video and image styling with static dark borders */
video, img {
    border: 3px solid #1a2a3a;
    border-radius: 18px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.7);
    background-color: #0a1219;
    transition: transform 0.4s ease, box-shadow 0.4s ease, border-color 0.4s ease;
}

video:hover, img:hover {
    transform: scale(1.04);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.9);
    border-color: #00c8a0;
}

/* Controls container with centered buttons */
.controls {
    margin-top: 3rem;
    display: flex;
    gap: 25px;
    flex-wrap: wrap;
    justify-content: center;
}

/* Button styling with vibrant green-cyan gradients and shimmer */
button {
    padding: 16px 32px;
    font-size: 1.2rem;
    font-weight: 700;
    cursor: pointer;
    border: none;
    border-radius: 12px;
    background: linear-gradient(45deg, #00c8a0, #1a8c70);
    color: #ffffff;
    transition: all 0.4s ease;
    box-shadow: 0 5px 20px rgba(0, 200, 150, 0.5);
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
    transition: transform 0.6s;
}

button:hover::before {
    transform: translateX(200%);
}

button:hover {
    background: linear-gradient(45deg, #00d8b0, #2a9c80);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 200, 150, 0.7);
}

button:disabled {
    background: #2a3a4a;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    opacity: 0.5;
}

/* Log container with shimmering effect */
.log-container {
    margin-top: 3rem;
    background: linear-gradient(145deg, #0a1219, #1a2e3b);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.8), inset 0 0 12px rgba(0, 200, 150, 0.2);
    width: 90%;
    max-width: 1400px;
    height: 250px;
    overflow-y: auto;
    border: 1px solid #1a2a3a;
    position: relative;
}

/* Shimmer effect for log container */
.log-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 200, 150, 0.1), transparent);
    animation: shimmer 6s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
    100% { transform: translateX(100%); }
}

/* Scrollbar styling with green-cyan accents */
.log-container::-webkit-scrollbar {
    width: 12px;
}

.log-container::-webkit-scrollbar-track {
    background: #0a1219;
    border-radius: 15px;
}

.log-container::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #00c8a0, #1a8c70);
    border-radius: 15px;
}

.log-container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #00d8b0, #2a9c80);
}

/* Preformatted text for logs with enhanced readability */
pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    color: #a0d8c8;
    font-size: 1rem;
    margin: 0;
    line-height: 1.6;
}

/* Animation for heading fade-in */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-15px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
    .video-container {
        flex-direction: column;
        align-items: center;
        padding: 20px;
    }

    video, img {
        width: 100%;
        max-width: 500px;
        height: auto;
    }

    h1 {
        font-size: 2.4rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    .log-container {
        width: 95%;
        height: 200px;
    }

    button {
        padding: 14px 24px;
        font-size: 1.1rem;
    }
}

/* Ultra-small screens */
@media (max-width: 480px) {
    body {
        padding: 20px;
    }

    h1 {
        font-size: 2rem;
    }

    .video-container {
        gap: 20px;
    }

    video, img {
        max-width: 100%;
    }

    button {
        padding: 12px 20px;
        font-size: 1rem;
    }
}