# Requirements for the attendance tracking functionality
# Core web framework
fastapi>=0.104.0
uvicorn>=0.24.0
starlette>=0.27.0

# For file responses and CORS
python-multipart>=0.0.6

# For Gradio interface
gradio>=4.0.0
requests>=2.31.0

# Computer vision and ML
ultralytics>=8.0.0
opencv-python>=4.8.0
torch>=2.0.0
torchvision>=0.15.0
numpy>=1.24.0

# For ngrok (optional, for public access)
pyngrok>=7.0.0

# Data handling
pandas>=2.0.0
